from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, send_from_directory
import os
import json
import datetime
from werkzeug.utils import secure_filename
from app.models.database import db
from app.models.models import Task, Tool, TaskTool, CheckResult, ToolTemplate, TemplateToolItem
from app.utils.ai_service import analyze_image
from app.utils.tool_generator import generate_tools_for_task
from app.utils.pdf_generator import generate_pdf

main_bp = Blueprint('main', __name__)

def register_routes(app):
    """注册所有路由"""
    app.register_blueprint(main_bp)

# 首页
@main_bp.route('/')
def index():
    return render_template('index.html')

# 任务列表
@main_bp.route('/tasks')
def task_list():
    tasks = Task.query.order_by(Task.created_at.desc()).all()
    return render_template('tasks.html', tasks=tasks)

# 创建任务
@main_bp.route('/tasks/create', methods=['GET', 'POST'])
def create_task():
    if request.method == 'POST':
        title = request.form.get('title')
        description = request.form.get('description')
        voltage_level = request.form.get('voltage_level')
        work_type = request.form.get('work_type')
        environment = request.form.get('environment')
        
        task = Task(
            title=title,
            description=description,
            voltage_level=voltage_level,
            work_type=work_type,
            environment=environment
        )
        db.session.add(task)
        db.session.commit()
        
        # 根据任务信息生成工器具清单
        generate_tools_for_task(task)
        
        return redirect(url_for('main.task_detail', task_id=task.id))
    
    return render_template('create_task.html')

# 任务详情
@main_bp.route('/tasks/<int:task_id>')
def task_detail(task_id):
    task = Task.query.get_or_404(task_id)
    task_tools = TaskTool.query.filter_by(task_id=task_id).all()
    
    # 计算准备进度
    total_tools = len(task_tools)
    prepared_tools = sum(1 for tt in task_tools if tt.prepared)
    progress = int(prepared_tools / total_tools * 100) if total_tools > 0 else 0
    
    return render_template('task_detail.html', task=task, task_tools=task_tools, progress=progress)

# 更新工具准备状态
@main_bp.route('/tasks/<int:task_id>/update_tool', methods=['POST'])
def update_tool(task_id):
    task_tool_id = request.form.get('task_tool_id')
    prepared = request.form.get('prepared') == 'true'
    actual_quantity = request.form.get('actual_quantity')
    receiver = request.form.get('receiver')
    remark = request.form.get('remark')
    
    task_tool = TaskTool.query.get_or_404(task_tool_id)
    task_tool.prepared = prepared
    
    if actual_quantity:
        task_tool.actual_quantity = actual_quantity
    
    if receiver:
        task_tool.receiver = receiver
        task_tool.received_at = datetime.datetime.now()
    
    if remark:
        task_tool.remark = remark
    
    db.session.commit()
    
    return jsonify({'success': True})

# 完成任务准备
@main_bp.route('/tasks/<int:task_id>/complete', methods=['POST'])
def complete_task(task_id):
    task = Task.query.get_or_404(task_id)
    task.status = '准备完成'
    db.session.commit()
    
    return redirect(url_for('main.task_detail', task_id=task_id))

# 导出PDF
@main_bp.route('/tasks/<int:task_id>/export_pdf')
def export_pdf(task_id):
    task = Task.query.get_or_404(task_id)
    pdf_path = generate_pdf(task)
    
    return jsonify({'pdf_url': pdf_path})

# AI核查页面
@main_bp.route('/tasks/<int:task_id>/check', methods=['GET', 'POST'])
def ai_check(task_id):
    task = Task.query.get_or_404(task_id)
    
    if request.method == 'POST':
        if 'images' not in request.files:
            return redirect(request.url)
        
        files = request.files.getlist('images')
        results = []
        
        for file in files:
            if file and file.filename:
                filename = secure_filename(file.filename)
                timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
                filename = f"{timestamp}_{filename}"
                
                # 确保上传目录存在 - 保存到static/uploads目录
                upload_folder = os.path.join(current_app.static_folder, 'uploads')
                os.makedirs(upload_folder, exist_ok=True)

                file_path = os.path.join(upload_folder, filename)
                file.save(file_path)

                # 调用AI服务分析图片
                relative_path = f'uploads/{filename}'  # 相对于static文件夹的路径
                ai_result = analyze_image(file_path, task)
                
                # 保存结果到数据库
                check_result = CheckResult(
                    task_id=task_id,
                    image_path=relative_path,
                    result_json=json.dumps(ai_result, ensure_ascii=False)
                )
                db.session.add(check_result)
                db.session.commit()
                
                results.append({
                    'image_path': relative_path,
                    'result': ai_result
                })
        
        return render_template('check_result.html', task=task, results=results)
    
    # 获取已有的核查结果
    check_results = CheckResult.query.filter_by(task_id=task_id).order_by(CheckResult.created_at.desc()).all()
    parsed_results = []
    
    for result in check_results:
        parsed_results.append({
            'image_path': result.image_path,
            'result': json.loads(result.result_json)
        })
    
    return render_template('ai_check.html', task=task, check_results=parsed_results)

# 手动添加工具
@main_bp.route('/tasks/<int:task_id>/add_tool', methods=['POST'])
def add_manual_tool(task_id):
    """手动添加工具到任务清单"""
    task = Task.query.get_or_404(task_id)

    tool_name = request.form.get('tool_name', '').strip()
    suggested_quantity = request.form.get('suggested_quantity', 1, type=int)
    remark = request.form.get('remark', '').strip()

    if not tool_name:
        return jsonify({'success': False, 'message': '工具名称不能为空'})

    # 创建手动添加的工具记录
    task_tool = TaskTool(
        task_id=task_id,
        tool_id=None,  # 手动添加的工具没有tool_id
        tool_name=tool_name,
        suggested_quantity=suggested_quantity,
        remark=remark,
        is_manual=True
    )

    db.session.add(task_tool)
    db.session.commit()

    return jsonify({'success': True, 'message': '工具添加成功'})

# 模板管理
@main_bp.route('/templates')
def template_list():
    """模板列表页面"""
    templates = ToolTemplate.query.order_by(ToolTemplate.priority.desc(), ToolTemplate.created_at.desc()).all()
    return render_template('templates.html', templates=templates)

@main_bp.route('/templates/create', methods=['GET', 'POST'])
def create_template():
    """创建模板"""
    if request.method == 'POST':
        name = request.form.get('name')
        title_pattern = request.form.get('title_pattern')
        description = request.form.get('description')
        priority = request.form.get('priority', 0, type=int)

        template = ToolTemplate(
            name=name,
            title_pattern=title_pattern,
            description=description,
            priority=priority
        )
        db.session.add(template)
        db.session.commit()

        return redirect(url_for('main.template_detail', template_id=template.id))

    return render_template('create_template.html')

@main_bp.route('/templates/<int:template_id>')
def template_detail(template_id):
    """模板详情"""
    template = ToolTemplate.query.get_or_404(template_id)
    template_tools = TemplateToolItem.query.filter_by(template_id=template_id).all()
    all_tools = Tool.query.all()  # 用于添加工具的下拉列表
    return render_template('template_detail.html', template=template, template_tools=template_tools, all_tools=all_tools)

@main_bp.route('/templates/<int:template_id>/add_tool', methods=['POST'])
def add_template_tool(template_id):
    """向模板添加工具"""
    template = ToolTemplate.query.get_or_404(template_id)

    tool_type = request.form.get('tool_type')  # 'system' 或 'manual'
    suggested_quantity = request.form.get('suggested_quantity', 1, type=int)
    remark = request.form.get('remark', '').strip()

    if tool_type == 'system':
        tool_id = request.form.get('tool_id', type=int)
        if not tool_id:
            return jsonify({'success': False, 'message': '请选择系统工具'})

        template_tool = TemplateToolItem(
            template_id=template_id,
            tool_id=tool_id,
            suggested_quantity=suggested_quantity,
            remark=remark,
            is_manual=False
        )
    else:  # manual
        tool_name = request.form.get('tool_name', '').strip()
        if not tool_name:
            return jsonify({'success': False, 'message': '请输入工具名称'})

        template_tool = TemplateToolItem(
            template_id=template_id,
            tool_name=tool_name,
            suggested_quantity=suggested_quantity,
            remark=remark,
            is_manual=True
        )

    db.session.add(template_tool)
    db.session.commit()

    return jsonify({'success': True, 'message': '工具添加成功'})

@main_bp.route('/templates/<int:template_id>/delete_tool/<int:tool_item_id>', methods=['POST'])
def delete_template_tool(template_id, tool_item_id):
    """从模板删除工具"""
    template_tool = TemplateToolItem.query.filter_by(id=tool_item_id, template_id=template_id).first_or_404()
    db.session.delete(template_tool)
    db.session.commit()

    return jsonify({'success': True, 'message': '工具删除成功'})

@main_bp.route('/templates/<int:template_id>/toggle', methods=['POST'])
def toggle_template(template_id):
    """启用/禁用模板"""
    template = ToolTemplate.query.get_or_404(template_id)
    template.is_active = not template.is_active
    db.session.commit()

    status = '启用' if template.is_active else '禁用'
    return jsonify({'success': True, 'message': f'模板已{status}'})

# 处理上传的图片文件
@main_bp.route('/uploads/<filename>')
def uploaded_file(filename):
    """提供上传图片的访问路由"""
    upload_folder = os.path.join(current_app.static_folder, 'uploads')
    return send_from_directory(upload_folder, filename)
